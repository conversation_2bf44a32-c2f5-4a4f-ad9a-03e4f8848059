from .oct_key import <PERSON><PERSON><PERSON>
from .rsa_key import <PERSON><PERSON><PERSON><PERSON>
from .ec_key import <PERSON><PERSON><PERSON>
from .jws_algs import <PERSON><PERSON>_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .jwe_algs import JWE_ALG_ALGORITHMS, AESAlgorithm, ECDHESAlgorithm, u32be_len_input
from .jwe_encs import J<PERSON><PERSON>_<PERSON><PERSON>_ALGORITHMS, CBCHS2EncAlgorithm
from .jwe_zips import DeflateZipAlgorithm


def register_jws_rfc7518(cls):
    for algorithm in JWS_ALGORITHMS:
        cls.register_algorithm(algorithm)


def register_jwe_rfc7518(cls):
    for algorithm in JWE_ALG_ALGORITHMS:
        cls.register_algorithm(algorithm)

    for algorithm in JWE_ENC_ALGORITHMS:
        cls.register_algorithm(algorithm)

    cls.register_algorithm(DeflateZipAlgorithm())


__all__ = [
    'register_jws_rfc7518',
    'register_jwe_rfc7518',
    '<PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON>',
    'u32be_len_input',
    'AES<PERSON>lgorithm',
    'ECD<PERSON><PERSON><PERSON>lgorithm',
    'CBCHS2EncAlgorithm',
]
