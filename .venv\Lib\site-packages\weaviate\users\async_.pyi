from typing import Dict, List, Literal, Optional, Union, overload

from typing_extensions import deprecated

from weaviate.connect.v4 import ConnectionAsync
from weaviate.rbac.models import Role, RoleBase
from weaviate.users.base import _BaseExecutor, _UsersDBExecutor, _UsersExecutor, _UsersOIDCExecutor
from weaviate.users.users import USER_TYPE, OwnUser, UserDB

class _BaseAsync(_BaseExecutor[ConnectionAsync]):
    async def _get_roles_of_user(
        self, user_id: str, user_type: USER_TYPE, include_permissions: bool
    ) -> Union[Dict[str, Role], Dict[str, RoleBase]]: ...
    async def _get_roles_of_user_deprecated(
        self, user_id: str
    ) -> Union[Dict[str, Role], Dict[str, RoleBase]]: ...
    async def _assign_roles_to_user(
        self, roles: List[str], user_id: str, user_type: Optional[USER_TYPE]
    ) -> None: ...
    async def _revoke_roles_from_user(
        self, roles: Union[str, List[str]], user_id: str, user_type: Optional[USER_TYPE]
    ) -> None: ...

class _UsersOIDCAsync(_UsersOIDCExecutor[ConnectionAsync]):
    @overload
    async def get_assigned_roles(
        self, *, user_id: str, include_permissions: Literal[False] = False
    ) -> Dict[str, RoleBase]: ...
    @overload
    async def get_assigned_roles(
        self, *, user_id: str, include_permissions: Literal[True]
    ) -> Dict[str, Role]: ...
    @overload
    async def get_assigned_roles(
        self, *, user_id: str, include_permissions: bool = False
    ) -> Union[Dict[str, Role], Dict[str, RoleBase]]: ...
    async def assign_roles(self, *, user_id: str, role_names: Union[str, List[str]]) -> None: ...
    async def revoke_roles(self, *, user_id: str, role_names: Union[str, List[str]]) -> None: ...

class _UsersDBAsync(_UsersDBExecutor[ConnectionAsync]):
    @overload
    async def get_assigned_roles(
        self, *, user_id: str, include_permissions: Literal[False] = False
    ) -> Dict[str, RoleBase]: ...
    @overload
    async def get_assigned_roles(
        self, *, user_id: str, include_permissions: Literal[True]
    ) -> Dict[str, Role]: ...
    @overload
    async def get_assigned_roles(
        self, *, user_id: str, include_permissions: bool = False
    ) -> Union[Dict[str, Role], Dict[str, RoleBase]]: ...
    async def assign_roles(self, *, user_id: str, role_names: Union[str, List[str]]) -> None: ...
    async def revoke_roles(self, *, user_id: str, role_names: Union[str, List[str]]) -> None: ...
    async def create(self, *, user_id: str) -> str: ...
    async def delete(self, *, user_id: str) -> bool: ...
    async def rotate_key(self, *, user_id: str) -> str: ...
    async def activate(self, *, user_id: str) -> bool: ...
    async def deactivate(self, *, user_id: str, revoke_key: bool = False) -> bool: ...
    async def get(self, *, user_id: str) -> Optional[UserDB]: ...
    async def list_all(self) -> List[UserDB]: ...

class _UsersAsync(_UsersExecutor[ConnectionAsync]):
    async def get_my_user(self) -> OwnUser: ...
    @deprecated(
        "This method is deprecated and will be removed in Q4 25.\n                Please use `users.db.get_assigned_roles` and/or `users.oidc.get_assigned_roles` instead."
    )
    async def get_assigned_roles(self, user_id: str) -> Dict[str, Role]: ...
    @deprecated(
        "This method is deprecated and will be removed in Q4 25.\n                Please use `users.db.assign_roles` and/or `users.oidc.assign_roles` instead."
    )
    async def assign_roles(self, *, user_id: str, role_names: Union[str, List[str]]) -> None: ...
    @deprecated(
        "This method is deprecated and will be removed in Q4 25.\n                Please use `users.db.revoke_roles` and/or `users.oidc.revoke_roles` instead."
    )
    async def revoke_roles(self, *, user_id: str, role_names: Union[str, List[str]]) -> None: ...
    @property
    def oidc(self) -> _UsersOIDCAsync: ...
    @property
    def db(self) -> _UsersDBAsync: ...
