# Weaviate Vector Database Viewer

This project provides multiple ways to view and interact with your Weaviate vector database containing embedded PDF document chunks.

## 🚀 Quick Start

### 1. Start Weaviate Server
```bash
docker-compose up -d
```

### 2. Create and Populate Database
```bash
python vector_embedding.py
```

### 3. View Database (Multiple Options)

#### Option A: Command Line Viewer
```bash
python view_vector_db.py
```

#### Option B: Interactive Mode in Main Script
```bash
python vector_embedding.py --view
```

#### Option C: Web Interface
```bash
python web_viewer.py
```
Then open: http://localhost:5000

## 📁 Files Overview

### Core Files
- `vector_embedding.py` - Main script to create embeddings and populate database
- `docker-compose.yml` - Weaviate server configuration
- `chunks.json` - Processed PDF chunks (created by chunking.py)

### Viewing Tools
- `view_vector_db.py` - Command-line interactive viewer
- `web_viewer.py` - Flask web application for browser-based viewing
- `templates/index.html` - Web interface template

## 🔧 Features

### Command Line Viewer (`view_vector_db.py`)
- ✅ View collection statistics
- ✅ List all objects with pagination
- ✅ Filter objects by source file
- ✅ Search similar content using vector similarity
- ✅ Check Weaviate connection status

### Web Interface (`web_viewer.py`)
- ✅ Beautiful dashboard with statistics
- ✅ Real-time connection status
- ✅ File list with chunk counts
- ✅ Vector similarity search
- ✅ Browse all objects
- ✅ Responsive design

### Enhanced Main Script (`vector_embedding.py`)
- ✅ Store embeddings in Weaviate
- ✅ Interactive viewing mode (`--view` flag)
- ✅ Collection statistics
- ✅ Search functionality

## 🎯 Usage Examples

### Search Similar Content
```python
# In command line viewer or web interface
Query: "camera configuration"
Results: Returns chunks about camera setup, configuration parameters, etc.
```

### View Statistics
- Total objects in database
- Number of source files
- Chunks per file
- Connection status

### Browse by File
- Filter chunks by specific PDF file
- See chunk numbers and content
- Navigate through large documents

## 🛠️ API Endpoints (Web Interface)

- `GET /` - Main dashboard
- `GET /api/stats` - Collection statistics
- `GET /api/objects` - List objects with pagination
- `POST /api/search` - Vector similarity search
- `GET /api/health` - Weaviate connection status

## 📊 Database Schema

**Collection Name:** `ChunkEmbeddings`

**Properties:**
- `source_file` (text) - Name of the PDF file
- `chunk_number` (int) - Sequential chunk number
- `content` (text) - Text content of the chunk
- Vector embeddings (1536 dimensions from OpenAI text-embedding-ada-002)

## 🔍 Search Capabilities

The vector database supports:
- **Semantic Search** - Find content by meaning, not just keywords
- **Similarity Scoring** - Results ranked by relevance
- **Cross-Document Search** - Search across all PDF files
- **Context Preservation** - Maintains document structure with chunk numbers

## 🐳 Docker Management

### Start Weaviate
```bash
docker-compose up -d
```

### Stop Weaviate
```bash
docker-compose down
```

### View Logs
```bash
docker-compose logs -f weaviate
```

### Check Status
```bash
docker-compose ps
```

## 🔧 Troubleshooting

### Weaviate Connection Issues
1. Ensure Docker is running
2. Check if Weaviate container is up: `docker-compose ps`
3. Verify port 8080 is not blocked
4. Check logs: `docker-compose logs weaviate`

### Empty Database
1. Run `python vector_embedding.py` to populate
2. Ensure `chunks.json` exists
3. Check OpenAI API key is valid

### Web Interface Issues
1. Install Flask: `pip install flask`
2. Check port 5000 is available
3. Ensure Weaviate is running

## 📈 Performance Notes

- **Embedding Generation**: ~1-2 seconds per chunk (OpenAI API)
- **Search Speed**: <100ms for similarity search
- **Storage**: ~4KB per chunk (including embeddings)
- **Scalability**: Tested with 500+ chunks

## 🔐 Security Notes

- OpenAI API key is embedded in code (for demo purposes)
- Web interface runs on all interfaces (0.0.0.0)
- No authentication implemented
- Suitable for local development only

## 🚀 Next Steps

This vector database is ready for:
- RAG (Retrieval Augmented Generation) chatbot
- Question-answering systems
- Document search applications
- Content recommendation engines

## 📞 Support

If you encounter issues:
1. Check Weaviate is running: `curl http://localhost:8080/v1/meta`
2. Verify database exists: Use any viewer tool
3. Check logs for errors
4. Ensure all dependencies are installed
