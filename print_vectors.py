"""
Simple script to print vector embeddings from Weaviate database
"""

import weaviate
import numpy as np

# Weaviate configuration
WEAVIATE_CLASS_NAME = "ChunkEmbeddings"

def get_weaviate_client():
    """Get Weaviate client connection."""
    return weaviate.connect_to_local()

def print_vectors(limit=5, show_full_vector=False):
    """Print vectors from the database."""
    try:
        with get_weaviate_client() as client:
            if not client.collections.exists(WEAVIATE_CLASS_NAME):
                print("❌ Collection 'ChunkEmbeddings' does not exist!")
                return
            
            collection = client.collections.get(WEAVIATE_CLASS_NAME)
            
            # Get objects with vectors
            response = collection.query.fetch_objects(
                limit=limit,
                include_vector=True
            )
            
            if not response.objects:
                print("❌ No vectors found in the database!")
                return
            
            print(f"🧮 VECTOR EMBEDDINGS FROM WEAVIATE")
            print("=" * 80)
            print(f"Found {len(response.objects)} vectors")
            print("=" * 80)
            
            for i, obj in enumerate(response.objects, 1):
                # Debug: print the actual object structure
                print(f"\n📊 VECTOR {i}")
                print("-" * 40)
                print(f"UUID: {obj.uuid}")

                props = obj.properties
                print(f"Source File: {props.get('source_file', 'N/A')}")
                print(f"Chunk Number: {props.get('chunk_number', 'N/A')}")
                print(f"Content Preview: {props.get('content', '')[:100]}...")

                # Check if vector exists and get it properly
                vector = None
                if hasattr(obj, 'vector') and obj.vector is not None:
                    vector_data = obj.vector
                    # Handle different vector formats
                    if isinstance(vector_data, dict) and 'default' in vector_data:
                        vector = vector_data['default']
                    elif isinstance(vector_data, list):
                        vector = vector_data
                    else:
                        vector = vector_data
                elif hasattr(obj, '_additional') and obj._additional and 'vector' in obj._additional:
                    vector = obj._additional['vector']

                if vector is not None and len(vector) > 0:
                    print(f"Vector Dimensions: {len(vector)}")

                    try:
                        # Convert to numpy array for calculations
                        vector_array = np.array(vector, dtype=float)
                        print("Vector Stats:")
                        print(f"  - Min: {np.min(vector_array):.6f}")
                        print(f"  - Max: {np.max(vector_array):.6f}")
                        print(f"  - Mean: {np.mean(vector_array):.6f}")
                        print(f"  - Std: {np.std(vector_array):.6f}")
                        print(f"  - Norm: {np.linalg.norm(vector_array):.6f}")

                        # Vector preview (first 10 dimensions)
                        preview = [f"{float(v):.6f}" for v in vector[:10]]
                        ellipsis = "..." if len(vector) > 10 else ""
                        print(f"Vector Preview (first 10): [{', '.join(preview)}{ellipsis}]")

                        if show_full_vector:
                            print("Full Vector:")
                            # Print vector in chunks of 10 for readability
                            for j in range(0, len(vector), 10):
                                chunk = vector[j:j+10]
                                chunk_str = [f"{float(v):.6f}" for v in chunk]
                                end_idx = j + len(chunk) - 1
                                print(f"  [{j:4d}-{end_idx:4d}]: [{', '.join(chunk_str)}]")
                    except Exception as e:
                        print(f"❌ Error processing vector: {e}")
                        print(f"Vector type: {type(vector)}")
                        print(f"Vector sample: {vector[:5] if len(vector) > 5 else vector}")
                else:
                    print("❌ No vector data found for this object")
                    print(f"Object attributes: {dir(obj)}")
                    if hasattr(obj, '_additional'):
                        print(f"Additional data: {obj._additional}")
                
                print("-" * 40)
    
    except Exception as e:
        print(f"❌ Error: {e}")
        print("💡 Make sure Weaviate is running: docker-compose up -d")

def print_vector_comparison(limit=3):
    """Print vectors with similarity comparison."""
    try:
        with get_weaviate_client() as client:
            collection = client.collections.get(WEAVIATE_CLASS_NAME)
            
            response = collection.query.fetch_objects(
                limit=limit,
                include_vector=True
            )
            
            if len(response.objects) < 2:
                print("❌ Need at least 2 vectors for comparison")
                return
            
            vectors = []
            metadata = []
            
            for obj in response.objects:
                if hasattr(obj, 'vector') and obj.vector:
                    vectors.append(np.array(obj.vector))
                    metadata.append({
                        'uuid': str(obj.uuid)[:8],
                        'file': obj.properties.get('source_file', 'N/A'),
                        'chunk': obj.properties.get('chunk_number', 'N/A')
                    })
            
            print(f"\n🔍 VECTOR SIMILARITY COMPARISON")
            print("=" * 60)
            
            # Calculate cosine similarities
            for i in range(len(vectors)):
                for j in range(i+1, len(vectors)):
                    # Cosine similarity
                    dot_product = np.dot(vectors[i], vectors[j])
                    norm_i = np.linalg.norm(vectors[i])
                    norm_j = np.linalg.norm(vectors[j])
                    cosine_sim = dot_product / (norm_i * norm_j)
                    
                    # Euclidean distance
                    euclidean_dist = np.linalg.norm(vectors[i] - vectors[j])
                    
                    print(f"\nVector {i+1} vs Vector {j+1}:")
                    print(f"  {metadata[i]['file']} (chunk {metadata[i]['chunk']}) vs")
                    print(f"  {metadata[j]['file']} (chunk {metadata[j]['chunk']})")
                    print(f"  Cosine Similarity: {cosine_sim:.6f}")
                    print(f"  Euclidean Distance: {euclidean_dist:.6f}")
                    print(f"  Similarity %: {cosine_sim * 100:.2f}%")
    
    except Exception as e:
        print(f"❌ Error in comparison: {e}")

def main():
    """Main function with menu."""
    print("🧮 WEAVIATE VECTOR PRINTER")
    print("=" * 50)
    
    while True:
        print("\nOptions:")
        print("1. Print vectors (preview)")
        print("2. Print vectors (full)")
        print("3. Print vector comparison")
        print("4. Print specific vector by index")
        print("5. Exit")
        
        choice = input("\nEnter choice (1-5): ").strip()
        
        if choice == "1":
            limit = input("How many vectors to show? (default 5): ").strip()
            limit = int(limit) if limit.isdigit() else 5
            print_vectors(limit=limit, show_full_vector=False)
            
        elif choice == "2":
            limit = input("How many vectors to show? (default 3): ").strip()
            limit = int(limit) if limit.isdigit() else 3
            print_vectors(limit=limit, show_full_vector=True)
            
        elif choice == "3":
            limit = input("How many vectors to compare? (default 3): ").strip()
            limit = int(limit) if limit.isdigit() else 3
            print_vector_comparison(limit=limit)
            
        elif choice == "4":
            index = input("Enter vector index (1-based): ").strip()
            if index.isdigit():
                print_vectors(limit=int(index), show_full_vector=True)
            else:
                print("❌ Invalid index")
                
        elif choice == "5":
            print("👋 Goodbye!")
            break
            
        else:
            print("❌ Invalid choice")

if __name__ == "__main__":
    main()
