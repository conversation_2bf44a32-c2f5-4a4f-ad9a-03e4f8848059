<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weaviate Vector Database Viewer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #4facfe;
        }
        
        .stat-card h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #4facfe;
        }
        
        .search-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .search-form {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .search-input {
            flex: 1;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #4facfe;
        }
        
        .btn {
            padding: 12px 24px;
            background: #4facfe;
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #3a8bfd;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .results {
            margin-top: 20px;
        }
        
        .result-item {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
        }
        
        .result-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .result-meta {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 10px;
        }
        
        .result-content {
            line-height: 1.6;
            color: #333;
        }
        
        .similarity-score {
            background: #28a745;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: bold;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .file-list {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .file-item {
            padding: 10px 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .file-item:last-child {
            border-bottom: none;
        }
        
        .file-item:hover {
            background: #f8f9fa;
        }
        
        .chunk-count {
            background: #e9ecef;
            color: #495057;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ Weaviate Vector Database</h1>
            <p>View and search your embedded document chunks</p>
        </div>
        
        <div class="content">
            <!-- Statistics Section -->
            <div class="stats-grid" id="stats-grid">
                <div class="stat-card">
                    <h3>📊 Total Objects</h3>
                    <div class="stat-value" id="total-objects">Loading...</div>
                </div>
                <div class="stat-card">
                    <h3>📁 Source Files</h3>
                    <div class="stat-value" id="total-files">Loading...</div>
                </div>
                <div class="stat-card">
                    <h3>🔗 Connection Status</h3>
                    <div class="stat-value" id="connection-status">Checking...</div>
                </div>
            </div>
            
            <!-- File List Section -->
            <div class="search-section">
                <h3>📁 Source Files</h3>
                <div class="file-list" id="file-list">
                    <div class="loading">Loading files...</div>
                </div>
            </div>
            
            <!-- Search Section -->
            <div class="search-section">
                <h3>🔍 Search Similar Content</h3>
                <div class="search-form">
                    <input type="text" class="search-input" id="search-query" placeholder="Enter your search query...">
                    <button class="btn" onclick="searchContent()">Search</button>
                    <button class="btn btn-secondary" onclick="loadObjects()">View All</button>
                </div>
                
                <div id="search-results"></div>
            </div>
        </div>
    </div>

    <script>
        // Load initial data
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            checkHealth();
        });
        
        async function loadStats() {
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();
                
                if (data.error) {
                    document.getElementById('total-objects').textContent = 'Error';
                    document.getElementById('total-files').textContent = 'Error';
                    return;
                }
                
                document.getElementById('total-objects').textContent = data.total_objects;
                document.getElementById('total-files').textContent = data.total_files;
                
                // Display file list
                const fileList = document.getElementById('file-list');
                fileList.innerHTML = '';
                
                for (const [filename, count] of Object.entries(data.files)) {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'file-item';
                    fileItem.innerHTML = `
                        <span>${filename}</span>
                        <span class="chunk-count">${count} chunks</span>
                    `;
                    fileList.appendChild(fileItem);
                }
                
            } catch (error) {
                console.error('Error loading stats:', error);
                document.getElementById('total-objects').textContent = 'Error';
                document.getElementById('total-files').textContent = 'Error';
            }
        }
        
        async function checkHealth() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                
                const statusElement = document.getElementById('connection-status');
                if (data.status === 'healthy') {
                    statusElement.textContent = '✅ Online';
                    statusElement.style.color = '#28a745';
                } else {
                    statusElement.textContent = '❌ Offline';
                    statusElement.style.color = '#dc3545';
                }
            } catch (error) {
                document.getElementById('connection-status').textContent = '❌ Error';
                document.getElementById('connection-status').style.color = '#dc3545';
            }
        }
        
        async function searchContent() {
            const query = document.getElementById('search-query').value.trim();
            if (!query) {
                alert('Please enter a search query');
                return;
            }
            
            const resultsDiv = document.getElementById('search-results');
            resultsDiv.innerHTML = '<div class="loading">Searching...</div>';
            
            try {
                const response = await fetch('/api/search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ query: query, limit: 5 })
                });
                
                const data = await response.json();
                
                if (data.error) {
                    resultsDiv.innerHTML = `<div class="error">Error: ${data.error}</div>`;
                    return;
                }
                
                displayResults(data.results, 'search');
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        async function loadObjects() {
            const resultsDiv = document.getElementById('search-results');
            resultsDiv.innerHTML = '<div class="loading">Loading objects...</div>';
            
            try {
                const response = await fetch('/api/objects?limit=10');
                const data = await response.json();
                
                if (data.error) {
                    resultsDiv.innerHTML = `<div class="error">Error: ${data.error}</div>`;
                    return;
                }
                
                displayResults(data.objects, 'list');
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        function displayResults(results, type) {
            const resultsDiv = document.getElementById('search-results');
            
            if (results.length === 0) {
                resultsDiv.innerHTML = '<div class="loading">No results found</div>';
                return;
            }
            
            let html = `<h4>${type === 'search' ? 'Search Results' : 'All Objects'} (${results.length})</h4>`;
            
            results.forEach((item, index) => {
                const similarity = item.similarity ? 
                    `<span class="similarity-score">${item.similarity.toFixed(1)}% match</span>` : '';
                
                html += `
                    <div class="result-item">
                        <div class="result-header">
                            <strong>Chunk ${item.chunk_number}</strong>
                            ${similarity}
                        </div>
                        <div class="result-meta">
                            📁 ${item.source_file} | 🆔 ${item.uuid.substring(0, 8)}...
                        </div>
                        <div class="result-content">
                            ${item.content}
                        </div>
                    </div>
                `;
            });
            
            resultsDiv.innerHTML = html;
        }
        
        // Allow Enter key to trigger search
        document.getElementById('search-query').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchContent();
            }
        });
    </script>
</body>
</html>
