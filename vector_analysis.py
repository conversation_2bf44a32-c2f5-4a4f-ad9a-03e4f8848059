"""
Vector Analysis Tool
Analyze vector embeddings in the Weaviate database.
"""

import weaviate
import numpy as np
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import matplotlib.pyplot as plt
import seaborn as sns

# Weaviate configuration
WEAVIATE_CLASS_NAME = "ChunkEmbeddings"

def get_weaviate_client():
    """Get Weaviate client connection."""
    return weaviate.connect_to_local()

def get_all_vectors():
    """Retrieve all vectors from the database."""
    vectors = []
    metadata = []
    
    with get_weaviate_client() as client:
        collection = client.collections.get(WEAVIATE_CLASS_NAME)
        
        # Get all objects with vectors
        response = collection.query.fetch_objects(
            limit=1000,
            include_vector=True
        )
        
        for obj in response.objects:
            if hasattr(obj, 'vector') and obj.vector:
                vectors.append(obj.vector)
                metadata.append({
                    'uuid': str(obj.uuid),
                    'source_file': obj.properties['source_file'],
                    'chunk_number': obj.properties['chunk_number'],
                    'content_preview': obj.properties['content'][:50] + '...'
                })
    
    return np.array(vectors), metadata

def analyze_vector_statistics(vectors):
    """Analyze basic statistics of vectors."""
    print("📊 Vector Statistics Analysis")
    print("=" * 50)
    print(f"Total vectors: {len(vectors)}")
    print(f"Vector dimensions: {vectors.shape[1] if len(vectors) > 0 else 0}")
    
    if len(vectors) == 0:
        print("❌ No vectors found!")
        return
    
    print(f"Vector shape: {vectors.shape}")
    print(f"Data type: {vectors.dtype}")
    
    # Basic statistics
    print(f"\n📈 Statistical Summary:")
    print(f"Mean: {np.mean(vectors):.6f}")
    print(f"Std Dev: {np.std(vectors):.6f}")
    print(f"Min value: {np.min(vectors):.6f}")
    print(f"Max value: {np.max(vectors):.6f}")
    
    # Per-dimension statistics
    print(f"\n📊 Per-dimension Analysis:")
    dim_means = np.mean(vectors, axis=0)
    dim_stds = np.std(vectors, axis=0)
    
    print(f"Dimension means - Min: {np.min(dim_means):.6f}, Max: {np.max(dim_means):.6f}")
    print(f"Dimension std devs - Min: {np.min(dim_stds):.6f}, Max: {np.max(dim_stds):.6f}")
    
    # Vector norms
    norms = np.linalg.norm(vectors, axis=1)
    print(f"\n📏 Vector Norms:")
    print(f"Mean norm: {np.mean(norms):.6f}")
    print(f"Std norm: {np.std(norms):.6f}")
    print(f"Min norm: {np.min(norms):.6f}")
    print(f"Max norm: {np.max(norms):.6f}")

def compute_similarity_matrix(vectors, sample_size=50):
    """Compute similarity matrix for a sample of vectors."""
    if len(vectors) == 0:
        return None
    
    # Sample vectors if too many
    if len(vectors) > sample_size:
        indices = np.random.choice(len(vectors), sample_size, replace=False)
        sample_vectors = vectors[indices]
    else:
        sample_vectors = vectors
    
    # Compute cosine similarity matrix
    normalized_vectors = sample_vectors / np.linalg.norm(sample_vectors, axis=1, keepdims=True)
    similarity_matrix = np.dot(normalized_vectors, normalized_vectors.T)
    
    return similarity_matrix

def analyze_vector_clusters(vectors, metadata, n_components=2):
    """Analyze vector clusters using dimensionality reduction."""
    if len(vectors) < 2:
        print("❌ Need at least 2 vectors for cluster analysis")
        return
    
    print(f"\n🔍 Cluster Analysis (reducing to {n_components}D)")
    print("=" * 50)
    
    # PCA
    try:
        pca = PCA(n_components=n_components)
        pca_result = pca.fit_transform(vectors)
        
        print(f"📊 PCA Results:")
        print(f"Explained variance ratio: {pca.explained_variance_ratio_}")
        print(f"Total explained variance: {sum(pca.explained_variance_ratio_):.4f}")
        
        # Group by source file
        file_groups = {}
        for i, meta in enumerate(metadata):
            file_name = meta['source_file']
            if file_name not in file_groups:
                file_groups[file_name] = []
            file_groups[file_name].append(i)
        
        print(f"\n📁 Vectors by source file:")
        for file_name, indices in file_groups.items():
            print(f"  {file_name}: {len(indices)} vectors")
            
            # Calculate centroid for this file
            file_vectors = pca_result[indices]
            centroid = np.mean(file_vectors, axis=0)
            print(f"    Centroid: [{', '.join([f'{x:.4f}' for x in centroid])}]")
            
            # Calculate spread
            distances = np.linalg.norm(file_vectors - centroid, axis=1)
            print(f"    Spread (std): {np.std(distances):.4f}")
        
        return pca_result, file_groups
        
    except Exception as e:
        print(f"❌ Error in PCA analysis: {e}")
        return None, None

def find_most_similar_vectors(vectors, metadata, top_k=5):
    """Find pairs of most similar vectors."""
    if len(vectors) < 2:
        return
    
    print(f"\n🔗 Most Similar Vector Pairs (Top {top_k})")
    print("=" * 50)
    
    # Normalize vectors for cosine similarity
    normalized_vectors = vectors / np.linalg.norm(vectors, axis=1, keepdims=True)
    
    # Compute all pairwise similarities
    similarities = np.dot(normalized_vectors, normalized_vectors.T)
    
    # Get upper triangle (avoid diagonal and duplicates)
    upper_triangle = np.triu(similarities, k=1)
    
    # Find top k similarities
    flat_indices = np.argpartition(upper_triangle.flatten(), -top_k)[-top_k:]
    flat_indices = flat_indices[np.argsort(upper_triangle.flatten()[flat_indices])][::-1]
    
    for rank, flat_idx in enumerate(flat_indices, 1):
        i, j = np.unravel_index(flat_idx, upper_triangle.shape)
        similarity = similarities[i, j]
        
        print(f"\n{rank}. Similarity: {similarity:.6f}")
        print(f"   Vector A: {metadata[i]['source_file']} (Chunk {metadata[i]['chunk_number']})")
        print(f"   Content: {metadata[i]['content_preview']}")
        print(f"   Vector B: {metadata[j]['source_file']} (Chunk {metadata[j]['chunk_number']})")
        print(f"   Content: {metadata[j]['content_preview']}")

def main():
    """Main analysis function."""
    print("🧮 Vector Embeddings Analysis Tool")
    print("=" * 60)
    
    try:
        # Get all vectors
        print("📥 Loading vectors from Weaviate...")
        vectors, metadata = get_all_vectors()
        
        if len(vectors) == 0:
            print("❌ No vectors found in the database!")
            print("💡 Make sure you've run vector_embedding.py first.")
            return
        
        print(f"✅ Loaded {len(vectors)} vectors")
        
        # Basic statistics
        analyze_vector_statistics(vectors)
        
        # Similarity analysis
        print(f"\n🔍 Computing similarity matrix...")
        similarity_matrix = compute_similarity_matrix(vectors, sample_size=20)
        
        if similarity_matrix is not None:
            print(f"Similarity matrix shape: {similarity_matrix.shape}")
            print(f"Average similarity: {np.mean(similarity_matrix):.6f}")
            print(f"Max similarity (excluding diagonal): {np.max(similarity_matrix - np.eye(similarity_matrix.shape[0])):.6f}")
        
        # Cluster analysis
        analyze_vector_clusters(vectors, metadata)
        
        # Most similar pairs
        find_most_similar_vectors(vectors, metadata)
        
        print(f"\n✅ Analysis complete!")
        print(f"💡 Use the web viewer at http://localhost:8000 to explore individual vectors")
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
        print("💡 Make sure Weaviate is running: docker-compose up -d")

if __name__ == "__main__":
    main()
