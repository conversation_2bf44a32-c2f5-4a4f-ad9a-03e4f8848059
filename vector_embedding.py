from openai import OpenAI
import json
import weaviate
from weaviate.util import generate_uuid5

# Initialize OpenAI client
client_openai = OpenAI(
    api_key="********************************************************************************************************************************************************************"
)

# Weaviate configuration
WEAVIATE_CLASS_NAME = "ChunkEmbeddings"  # Class to store embeddings

def load_chunks(json_file):
    """Load chunked text data from a JSON file."""
    with open(json_file, "r", encoding="utf-8") as f:
        return json.load(f)

def get_embedding(text):
    """Get vector embeddings for a given text using OpenAI's embedding API."""
    response = client_openai.embeddings.create(
        input=text,
        model="text-embedding-ada-002"
    )
    return response.data[0].embedding

def setup_weaviate_schema(client):
    """Setup Weaviate schema for storing embeddings."""
    # Check if collection already exists
    if client.collections.exists(WEAVIATE_CLASS_NAME):
        print(f"Collection '{WEAVIATE_CLASS_NAME}' already exists.")
        return

    # Create collection with v4 API
    client.collections.create(
        name=WEAVIATE_CLASS_NAME,
        description="Stores embeddings for chunked text.",
        properties=[
            weaviate.classes.config.Property(
                name="source_file",
                data_type=weaviate.classes.config.DataType.TEXT,
                description="Name of the source file."
            ),
            weaviate.classes.config.Property(
                name="chunk_number",
                data_type=weaviate.classes.config.DataType.INT,
                description="Chunk number in the source file."
            ),
            weaviate.classes.config.Property(
                name="content",
                data_type=weaviate.classes.config.DataType.TEXT,
                description="Text content of the chunk."
            ),
        ]
    )
    print(f"Created collection '{WEAVIATE_CLASS_NAME}'.")

def store_embeddings_in_weaviate(chunks, client):
    """Store embeddings in Weaviate."""
    collection = client.collections.get(WEAVIATE_CLASS_NAME)

    for chunk in chunks:
        try:
            embedding = get_embedding(chunk["content"])
            uuid = generate_uuid5(chunk["content"])

            data_object = {
                "source_file": chunk["source_file"],
                "chunk_number": chunk["chunk_number"],
                "content": chunk["content"],
            }

            collection.data.insert(
                properties=data_object,
                vector=embedding,
                uuid=uuid
            )
            print(f"✅ Stored chunk {chunk['chunk_number']} from {chunk['source_file']}.")
        except Exception as e:
            print(f"❌ Error storing chunk {chunk['chunk_number']} from {chunk['source_file']}: {e}")

def main():
    # Load chunked data
    json_file = r"D:\ONLSOL_PROJ\chunks.json"  # Use raw string to avoid escape issues
    chunks = load_chunks(json_file)

    # Connect to Weaviate (embedded mode - no server required)
    with weaviate.connect_to_embedded() as client:
        # Setup schema
        setup_weaviate_schema(client)

        # Store embeddings
        store_embeddings_in_weaviate(chunks, client)

        print("✅ All embeddings stored successfully!")

if __name__ == "__main__":
    main()
