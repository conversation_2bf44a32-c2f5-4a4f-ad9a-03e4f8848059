from openai import OpenAI
import json
import weaviate
from weaviate.util import generate_uuid5

# Initialize OpenAI client
client_openai = OpenAI(
    api_key="********************************************************************************************************************************************************************"
)

# Weaviate configuration
WEAVIATE_CLASS_NAME = "ChunkEmbeddings"  # Class to store embeddings

def load_chunks(json_file):
    """Load chunked text data from a JSON file."""
    with open(json_file, "r", encoding="utf-8") as f:
        return json.load(f)

def get_embedding(text):
    """Get vector embeddings for a given text using OpenAI's embedding API."""
    response = openai.Embedding.create(
        input=text,
        model="text-embedding-ada-002"
    )
    return response["data"][0]["embedding"]

def setup_weaviate_schema(client):
    """Setup Weaviate schema for storing embeddings."""
    schema = {
        "class": WEAVIATE_CLASS_NAME,
        "description": "Stores embeddings for chunked text.",
        "properties": [
            {
                "name": "source_file",
                "dataType": ["text"],
                "description": "Name of the source file."
            },
            {
                "name": "chunk_number",
                "dataType": ["int"],
                "description": "Chunk number in the source file."
            },
            {
                "name": "content",
                "dataType": ["text"],
                "description": "Text content of the chunk."
            },
        ]
    }

    existing_classes = client.schema.get()
    class_names = [cls['class'] for cls in existing_classes['classes']]

    if WEAVIATE_CLASS_NAME not in class_names:
        client.schema.create_class(schema)

def store_embeddings_in_weaviate(chunks, client):
    """Store embeddings in Weaviate."""
    for chunk in chunks:
        try:
            embedding = get_embedding(chunk["content"])
            uuid = generate_uuid5(chunk["content"])

            data_object = {
                "source_file": chunk["source_file"],
                "chunk_number": chunk["chunk_number"],
                "content": chunk["content"],
            }

            client.data_object.create(
                data_object=data_object,
                class_name=WEAVIATE_CLASS_NAME,
                vector=embedding,
                uuid=uuid
            )
            print(f"✅ Stored chunk {chunk['chunk_number']} from {chunk['source_file']}.")
        except Exception as e:
            print(f"❌ Error storing chunk {chunk['chunk_number']} from {chunk['source_file']}: {e}")

def main():
    # Load chunked data
    json_file = r"D:\ONLSOL_PROJ\chunks.json"  # Use raw string to avoid escape issues
    chunks = load_chunks(json_file)

    # Connect to Weaviate (embedded mode - no server required)
    client = weaviate.connect_to_embedded()

    # Setup schema
    setup_weaviate_schema(client)

    # Store embeddings
    store_embeddings_in_weaviate(chunks, client)

if __name__ == "__main__":
    main()
