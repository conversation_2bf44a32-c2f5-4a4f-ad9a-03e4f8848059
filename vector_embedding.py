from openai import OpenAI
import json
import weaviate
from weaviate.util import generate_uuid5

# Initialize OpenAI client
client_openai = OpenAI(
    api_key="********************************************************************************************************************************************************************"
)

# Weaviate configuration
WEAVIATE_CLASS_NAME = "ChunkEmbeddings"  # Class to store embeddings

def load_chunks(json_file):
    """Load chunked text data from a JSON file."""
    with open(json_file, "r", encoding="utf-8") as f:
        return json.load(f)

def get_embedding(text):
    """Get vector embeddings for a given text using OpenAI's embedding API."""
    response = client_openai.embeddings.create(
        input=text,
        model="text-embedding-ada-002"
    )
    return response.data[0].embedding

def setup_weaviate_schema(client):
    """Setup Weaviate schema for storing embeddings."""
    # Check if collection already exists
    if client.collections.exists(WEAVIATE_CLASS_NAME):
        print(f"Collection '{WEAVIATE_CLASS_NAME}' already exists.")
        return

    # Create collection with v4 API
    client.collections.create(
        name=WEAVIATE_CLASS_NAME,
        description="Stores embeddings for chunked text.",
        properties=[
            weaviate.classes.config.Property(
                name="source_file",
                data_type=weaviate.classes.config.DataType.TEXT,
                description="Name of the source file."
            ),
            weaviate.classes.config.Property(
                name="chunk_number",
                data_type=weaviate.classes.config.DataType.INT,
                description="Chunk number in the source file."
            ),
            weaviate.classes.config.Property(
                name="content",
                data_type=weaviate.classes.config.DataType.TEXT,
                description="Text content of the chunk."
            ),
        ]
    )
    print(f"Created collection '{WEAVIATE_CLASS_NAME}'.")

def store_embeddings_in_weaviate(chunks, client):
    """Store embeddings in Weaviate."""
    collection = client.collections.get(WEAVIATE_CLASS_NAME)

    for chunk in chunks:
        try:
            embedding = get_embedding(chunk["content"])
            uuid = generate_uuid5(chunk["content"])

            data_object = {
                "source_file": chunk["source_file"],
                "chunk_number": chunk["chunk_number"],
                "content": chunk["content"],
            }

            collection.data.insert(
                properties=data_object,
                vector=embedding,
                uuid=uuid
            )
            print(f"✅ Stored chunk {chunk['chunk_number']} from {chunk['source_file']}.")
        except Exception as e:
            print(f"❌ Error storing chunk {chunk['chunk_number']} from {chunk['source_file']}: {e}")

def view_collection_stats(client):
    """Display statistics about the collection."""
    try:
        collection = client.collections.get(WEAVIATE_CLASS_NAME)

        # Get collection info
        response = collection.aggregate.over_all(
            total_count=True
        )

        print(f"\n📊 Collection Statistics:")
        print(f"Collection Name: {WEAVIATE_CLASS_NAME}")
        print(f"Total Objects: {response.total_count}")

        # Get sample objects
        objects = collection.query.fetch_objects(limit=5)

        print(f"\n📄 Sample Objects (first 5):")
        for i, obj in enumerate(objects.objects, 1):
            print(f"\n{i}. UUID: {obj.uuid}")
            print(f"   Source File: {obj.properties['source_file']}")
            print(f"   Chunk Number: {obj.properties['chunk_number']}")
            print(f"   Content Preview: {obj.properties['content'][:100]}...")

    except Exception as e:
        print(f"❌ Error viewing collection stats: {e}")

def search_similar_content(client, query_text, limit=3):
    """Search for similar content using vector similarity."""
    try:
        collection = client.collections.get(WEAVIATE_CLASS_NAME)

        # Get embedding for the query
        query_embedding = get_embedding(query_text)

        # Perform vector search
        response = collection.query.near_vector(
            near_vector=query_embedding,
            limit=limit,
            return_metadata=weaviate.classes.query.MetadataQuery(distance=True)
        )

        print(f"\n🔍 Search Results for: '{query_text}'")
        print(f"Found {len(response.objects)} similar chunks:")

        for i, obj in enumerate(response.objects, 1):
            distance = obj.metadata.distance if obj.metadata else "N/A"
            print(f"\n{i}. Distance: {distance:.4f}")
            print(f"   Source: {obj.properties['source_file']}")
            print(f"   Chunk: {obj.properties['chunk_number']}")
            print(f"   Content: {obj.properties['content'][:200]}...")

    except Exception as e:
        print(f"❌ Error searching content: {e}")

def list_all_objects(client, limit=10):
    """List all objects in the collection."""
    try:
        collection = client.collections.get(WEAVIATE_CLASS_NAME)

        objects = collection.query.fetch_objects(limit=limit)

        print(f"\n📋 All Objects (showing first {limit}):")
        for i, obj in enumerate(objects.objects, 1):
            print(f"\n{i}. UUID: {obj.uuid}")
            print(f"   File: {obj.properties['source_file']}")
            print(f"   Chunk: {obj.properties['chunk_number']}")
            print(f"   Content: {obj.properties['content'][:150]}...")

    except Exception as e:
        print(f"❌ Error listing objects: {e}")

def interactive_menu(client):
    """Interactive menu for viewing and querying the vector database."""
    while True:
        print("\n" + "="*50)
        print("🗄️  WEAVIATE VECTOR DATABASE VIEWER")
        print("="*50)
        print("1. View Collection Statistics")
        print("2. List All Objects (first 10)")
        print("3. Search Similar Content")
        print("4. Exit")
        print("-"*50)

        choice = input("Enter your choice (1-4): ").strip()

        if choice == "1":
            view_collection_stats(client)
        elif choice == "2":
            list_all_objects(client)
        elif choice == "3":
            query = input("\nEnter search query: ").strip()
            if query:
                search_similar_content(client, query)
            else:
                print("❌ Please enter a valid query.")
        elif choice == "4":
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please enter 1-4.")

def main():
    import sys

    # Check command line arguments
    if len(sys.argv) > 1 and sys.argv[1] == "--view":
        # View mode - just connect and show interactive menu
        print("🔍 Connecting to Weaviate for viewing...")
        with weaviate.connect_to_local() as client:
            if not client.collections.exists(WEAVIATE_CLASS_NAME):
                print(f"❌ Collection '{WEAVIATE_CLASS_NAME}' does not exist.")
                print("Run the script without --view flag first to create and populate the database.")
                return
            interactive_menu(client)
        return

    # Normal mode - load and store embeddings
    print("📁 Loading chunked data...")
    json_file = r"D:\ONLSOL_PROJ\chunks.json"  # Use raw string to avoid escape issues
    chunks = load_chunks(json_file)
    print(f"✅ Loaded {len(chunks)} chunks from {json_file}")

    # Connect to Weaviate (local server)
    with weaviate.connect_to_local() as client:
        # Setup schema
        setup_weaviate_schema(client)

        # Store embeddings
        store_embeddings_in_weaviate(chunks, client)

        print("✅ All embeddings stored successfully!")

        # Show quick stats
        view_collection_stats(client)

        # Ask if user wants to explore the database
        explore = input("\n🔍 Would you like to explore the database interactively? (y/n): ").strip().lower()
        if explore in ['y', 'yes']:
            interactive_menu(client)

if __name__ == "__main__":
    main()
