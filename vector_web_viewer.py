"""
Simple HTTP Server for Weaviate Vector Database Viewer
A lightweight web interface to view and query the vector database.
"""

import http.server
import socketserver
import json
import urllib.parse
from openai import OpenAI
import weaviate

# Initialize OpenAI client
client_openai = OpenAI(
    api_key="********************************************************************************************************************************************************************"
)

# Weaviate configuration
WEAVIATE_CLASS_NAME = "ChunkEmbeddings"

def get_embedding(text):
    """Get vector embeddings for a given text using OpenAI's embedding API."""
    response = client_openai.embeddings.create(
        input=text,
        model="text-embedding-ada-002"
    )
    return response.data[0].embedding

def get_weaviate_client():
    """Get Weaviate client connection."""
    return weaviate.connect_to_local()

class VectorDBHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/':
            self.serve_main_page()
        elif self.path == '/api/stats':
            self.serve_stats()
        elif self.path.startswith('/api/objects'):
            self.serve_objects()
        elif self.path == '/api/health':
            self.serve_health()
        else:
            self.send_error(404, "Not Found")
    
    def do_POST(self):
        if self.path == '/api/search':
            self.serve_search()
        else:
            self.send_error(404, "Not Found")
    
    def serve_main_page(self):
        """Serve the main HTML page."""
        html_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weaviate Vector Embeddings Viewer</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh; padding: 20px;
        }
        .container {
            max-width: 1200px; margin: 0 auto; background: white;
            border-radius: 15px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white; padding: 30px; text-align: center;
        }
        .header h1 { font-size: 2.5em; margin-bottom: 10px; }
        .header p { font-size: 1.1em; opacity: 0.9; }
        .content { padding: 30px; }
        .stats-grid {
            display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px; margin-bottom: 30px;
        }
        .stat-card {
            background: #f8f9fa; padding: 20px; border-radius: 10px;
            border-left: 4px solid #4facfe;
        }
        .stat-card h3 { color: #333; margin-bottom: 10px; }
        .stat-value { font-size: 2em; font-weight: bold; color: #4facfe; }
        .search-section {
            background: #f8f9fa; padding: 25px; border-radius: 10px; margin-bottom: 30px;
        }
        .search-form { display: flex; gap: 10px; margin-bottom: 20px; }
        .search-input {
            flex: 1; padding: 12px; border: 2px solid #ddd;
            border-radius: 8px; font-size: 16px;
        }
        .search-input:focus { outline: none; border-color: #4facfe; }
        .btn {
            padding: 12px 24px; background: #4facfe; color: white;
            border: none; border-radius: 8px; cursor: pointer;
            font-size: 16px; transition: background 0.3s;
        }
        .btn:hover { background: #3a8bfd; }
        .btn-secondary { background: #6c757d; }
        .btn-secondary:hover { background: #5a6268; }
        .result-item {
            background: white; border: 1px solid #ddd; border-radius: 8px;
            padding: 20px; margin-bottom: 15px;
        }
        .result-header {
            display: flex; justify-content: space-between;
            align-items: center; margin-bottom: 10px;
        }
        .result-meta { font-size: 0.9em; color: #666; margin-bottom: 10px; }
        .result-content { line-height: 1.6; color: #333; }
        .vector-display {
            background: #f8f9fa; padding: 15px; border-radius: 8px;
            margin: 10px 0; font-family: 'Courier New', monospace;
        }
        .vector-preview {
            background: #e9ecef; padding: 8px; border-radius: 4px;
            font-size: 0.85em; margin: 5px 0;
        }
        .vector-stats {
            display: flex; gap: 15px; margin: 10px 0;
            font-size: 0.9em; color: #666;
        }
        .vector-full {
            max-height: 200px; overflow-y: auto;
            background: #f1f3f4; padding: 10px; border-radius: 4px;
            font-size: 0.8em; word-break: break-all;
        }
        .toggle-vector {
            background: #17a2b8; color: white; border: none;
            padding: 5px 10px; border-radius: 4px; cursor: pointer;
            font-size: 0.8em; margin: 5px 0;
        }
        .toggle-vector:hover { background: #138496; }
        .similarity-score {
            background: #28a745; color: white; padding: 4px 8px;
            border-radius: 4px; font-size: 0.8em; font-weight: bold;
        }
        .loading { text-align: center; padding: 20px; color: #666; }
        .error {
            background: #f8d7da; color: #721c24; padding: 15px;
            border-radius: 8px; margin: 10px 0;
        }
        .file-list {
            background: white; border: 1px solid #ddd; border-radius: 8px;
            max-height: 200px; overflow-y: auto;
        }
        .file-item {
            padding: 10px 15px; border-bottom: 1px solid #eee;
            display: flex; justify-content: space-between; align-items: center;
        }
        .file-item:last-child { border-bottom: none; }
        .file-item:hover { background: #f8f9fa; }
        .chunk-count {
            background: #e9ecef; color: #495057; padding: 2px 8px;
            border-radius: 12px; font-size: 0.8em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧮 Weaviate Vector Embeddings</h1>
            <p>View and analyze vector embeddings from your document chunks</p>
        </div>
        
        <div class="content">
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>📊 Total Objects</h3>
                    <div class="stat-value" id="total-objects">Loading...</div>
                </div>
                <div class="stat-card">
                    <h3>📁 Source Files</h3>
                    <div class="stat-value" id="total-files">Loading...</div>
                </div>
                <div class="stat-card">
                    <h3>🔗 Connection Status</h3>
                    <div class="stat-value" id="connection-status">Checking...</div>
                </div>
            </div>
            
            <div class="search-section">
                <h3>📁 Source Files</h3>
                <div class="file-list" id="file-list">
                    <div class="loading">Loading files...</div>
                </div>
            </div>
            
            <div class="search-section">
                <h3>🔍 Search Similar Vectors</h3>
                <div class="search-form">
                    <input type="text" class="search-input" id="search-query" placeholder="Enter your search query...">
                    <button class="btn" onclick="searchContent()">Search</button>
                    <button class="btn btn-secondary" onclick="loadObjects()">View All</button>
                </div>
                <div id="search-results"></div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            checkHealth();
        });
        
        async function loadStats() {
            try {
                const response = await fetch('/api/stats');
                const data = await response.json();
                
                if (data.error) {
                    document.getElementById('total-objects').textContent = 'Error';
                    document.getElementById('total-files').textContent = 'Error';
                    return;
                }
                
                document.getElementById('total-objects').textContent = data.total_objects;
                document.getElementById('total-files').textContent = data.total_files;
                
                const fileList = document.getElementById('file-list');
                fileList.innerHTML = '';
                
                for (const [filename, count] of Object.entries(data.files)) {
                    const fileItem = document.createElement('div');
                    fileItem.className = 'file-item';
                    fileItem.innerHTML = `
                        <span>${filename}</span>
                        <span class="chunk-count">${count} chunks</span>
                    `;
                    fileList.appendChild(fileItem);
                }
            } catch (error) {
                console.error('Error loading stats:', error);
                document.getElementById('total-objects').textContent = 'Error';
                document.getElementById('total-files').textContent = 'Error';
            }
        }
        
        async function checkHealth() {
            try {
                const response = await fetch('/api/health');
                const data = await response.json();
                
                const statusElement = document.getElementById('connection-status');
                if (data.status === 'healthy') {
                    statusElement.textContent = '✅ Online';
                    statusElement.style.color = '#28a745';
                } else {
                    statusElement.textContent = '❌ Offline';
                    statusElement.style.color = '#dc3545';
                }
            } catch (error) {
                document.getElementById('connection-status').textContent = '❌ Error';
                document.getElementById('connection-status').style.color = '#dc3545';
            }
        }
        
        async function searchContent() {
            const query = document.getElementById('search-query').value.trim();
            if (!query) {
                alert('Please enter a search query');
                return;
            }
            
            const resultsDiv = document.getElementById('search-results');
            resultsDiv.innerHTML = '<div class="loading">Searching...</div>';
            
            try {
                const response = await fetch('/api/search', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ query: query, limit: 5 })
                });
                
                const data = await response.json();
                
                if (data.error) {
                    resultsDiv.innerHTML = `<div class="error">Error: ${data.error}</div>`;
                    return;
                }
                
                displayResults(data.results, 'search');
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        async function loadObjects() {
            const resultsDiv = document.getElementById('search-results');
            resultsDiv.innerHTML = '<div class="loading">Loading objects...</div>';
            
            try {
                const response = await fetch('/api/objects?limit=10');
                const data = await response.json();
                
                if (data.error) {
                    resultsDiv.innerHTML = `<div class="error">Error: ${data.error}</div>`;
                    return;
                }
                
                displayResults(data.objects, 'list');
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        function displayResults(results, type) {
            const resultsDiv = document.getElementById('search-results');

            if (results.length === 0) {
                resultsDiv.innerHTML = '<div class="loading">No results found</div>';
                return;
            }

            let html = `<h4>${type === 'search' ? 'Search Results' : 'All Objects'} (${results.length})</h4>`;

            results.forEach((item, index) => {
                const similarity = item.similarity ?
                    `<span class="similarity-score">${item.similarity.toFixed(1)}% match</span>` : '';

                const vectorPreview = item.vector_preview ?
                    item.vector_preview.map(v => v.toFixed(4)).join(', ') : 'No vector data';

                const queryVectorPreview = item.query_vector ?
                    `<div class="vector-preview"><strong>Query Vector (first 10):</strong> [${item.query_vector.map(v => v.toFixed(4)).join(', ')}]</div>` : '';

                html += `
                    <div class="result-item">
                        <div class="result-header">
                            <strong>Chunk ${item.chunk_number}</strong>
                            ${similarity}
                        </div>
                        <div class="result-meta">
                            📁 ${item.source_file} | 🆔 ${item.uuid.substring(0, 8)}...
                        </div>
                        <div class="vector-stats">
                            <span>📊 Vector Length: ${item.vector_length || 'N/A'}</span>
                            <span>🔢 Dimensions: ${item.vector_length || 'N/A'}</span>
                            ${item.distance ? `<span>📏 Distance: ${item.distance.toFixed(6)}</span>` : ''}
                        </div>
                        ${queryVectorPreview}
                        <div class="vector-display">
                            <strong>Vector Embedding (first 10 dimensions):</strong>
                            <div class="vector-preview">[${vectorPreview}]</div>
                            <button class="toggle-vector" onclick="toggleFullVector(${index}, this)">Show Full Vector</button>
                            <div class="vector-full" id="full-vector-${index}" style="display: none;">
                                ${item.vector ? '[' + item.vector.map(v => v.toFixed(6)).join(', ') + ']' : 'No vector data'}
                            </div>
                        </div>
                        <div class="result-content">
                            <strong>Content Preview:</strong> ${item.content || 'No content'}
                        </div>
                    </div>
                `;
            });

            resultsDiv.innerHTML = html;
        }

        function toggleFullVector(index, button) {
            const fullVectorDiv = document.getElementById(`full-vector-${index}`);
            if (fullVectorDiv.style.display === 'none') {
                fullVectorDiv.style.display = 'block';
                button.textContent = 'Hide Full Vector';
            } else {
                fullVectorDiv.style.display = 'none';
                button.textContent = 'Show Full Vector';
            }
        }
        
        document.getElementById('search-query').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchContent();
            }
        });
    </script>
</body>
</html>
        """
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html')
        self.end_headers()
        self.wfile.write(html_content.encode())
    
    def serve_stats(self):
        """Serve collection statistics."""
        try:
            with get_weaviate_client() as client:
                if not client.collections.exists(WEAVIATE_CLASS_NAME):
                    self.send_json_response({'error': 'Collection does not exist'})
                    return
                
                collection = client.collections.get(WEAVIATE_CLASS_NAME)
                response = collection.aggregate.over_all(total_count=True)
                total_count = response.total_count
                
                objects = collection.query.fetch_objects(limit=1000)
                source_files = {}
                
                for obj in objects.objects:
                    source_file = obj.properties['source_file']
                    source_files[source_file] = source_files.get(source_file, 0) + 1
                
                self.send_json_response({
                    'total_objects': total_count,
                    'total_files': len(source_files),
                    'files': source_files
                })
        except Exception as e:
            self.send_json_response({'error': str(e)})
    
    def serve_objects(self):
        """Serve objects with pagination."""
        try:
            parsed_url = urllib.parse.urlparse(self.path)
            query_params = urllib.parse.parse_qs(parsed_url.query)

            limit = int(query_params.get('limit', [10])[0])
            source_file = query_params.get('source_file', [None])[0]

            with get_weaviate_client() as client:
                collection = client.collections.get(WEAVIATE_CLASS_NAME)

                if source_file:
                    response = collection.query.fetch_objects(
                        where=weaviate.classes.query.Filter.by_property("source_file").equal(source_file),
                        limit=limit,
                        include_vector=True
                    )
                else:
                    response = collection.query.fetch_objects(limit=limit, include_vector=True)

                objects = []
                for obj in response.objects:
                    vector = obj.vector if hasattr(obj, 'vector') and obj.vector else []
                    # Ensure vector is a list for JSON serialization
                    if vector and hasattr(vector, '__iter__'):
                        vector_list = list(vector)
                        vector_preview = vector_list[:10] if len(vector_list) >= 10 else vector_list
                    else:
                        vector_list = []
                        vector_preview = []

                    objects.append({
                        'uuid': str(obj.uuid),
                        'source_file': obj.properties['source_file'],
                        'chunk_number': obj.properties['chunk_number'],
                        'content': obj.properties['content'][:100] + '...' if len(obj.properties['content']) > 100 else obj.properties['content'],
                        'vector': vector_list,
                        'vector_length': len(vector_list),
                        'vector_preview': vector_preview
                    })

                self.send_json_response({'objects': objects})
        except Exception as e:
            self.send_json_response({'error': str(e)})
    
    def serve_search(self):
        """Search for similar content."""
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            query = data.get('query', '')
            limit = int(data.get('limit', 5))
            
            if not query:
                self.send_json_response({'error': 'Query is required'})
                return
            
            with get_weaviate_client() as client:
                collection = client.collections.get(WEAVIATE_CLASS_NAME)
                
                query_embedding = get_embedding(query)
                
                response = collection.query.near_vector(
                    near_vector=query_embedding,
                    limit=limit,
                    return_metadata=weaviate.classes.query.MetadataQuery(distance=True),
                    include_vector=True
                )

                results = []
                for obj in response.objects:
                    distance = obj.metadata.distance if obj.metadata else None
                    similarity = (1 - distance) * 100 if distance is not None else None
                    vector = obj.vector if hasattr(obj, 'vector') and obj.vector else []

                    # Ensure vector is a list for JSON serialization
                    if vector and hasattr(vector, '__iter__'):
                        vector_list = list(vector)
                        vector_preview = vector_list[:10] if len(vector_list) >= 10 else vector_list
                    else:
                        vector_list = []
                        vector_preview = []

                    # Ensure query_embedding is also properly handled
                    query_preview = query_embedding[:10] if len(query_embedding) >= 10 else query_embedding

                    results.append({
                        'uuid': str(obj.uuid),
                        'source_file': obj.properties['source_file'],
                        'chunk_number': obj.properties['chunk_number'],
                        'content': obj.properties['content'][:100] + '...' if len(obj.properties['content']) > 100 else obj.properties['content'],
                        'distance': distance,
                        'similarity': similarity,
                        'vector': vector_list,
                        'vector_length': len(vector_list),
                        'vector_preview': vector_preview,
                        'query_vector': query_preview
                    })
                
                self.send_json_response({'results': results})
        except Exception as e:
            self.send_json_response({'error': str(e)})
    
    def serve_health(self):
        """Check Weaviate health."""
        try:
            with get_weaviate_client() as client:
                if client.is_ready():
                    self.send_json_response({'status': 'healthy', 'message': 'Weaviate is accessible'})
                else:
                    self.send_json_response({'status': 'unhealthy', 'message': 'Weaviate is not ready'})
        except Exception as e:
            self.send_json_response({'status': 'error', 'message': str(e)})
    
    def send_json_response(self, data):
        """Send JSON response."""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json.dumps(data).encode())

def start_server(port=8000):
    """Start the HTTP server."""
    handler = VectorDBHandler
    
    with socketserver.TCPServer(("", port), handler) as httpd:
        print(f"🚀 Starting Weaviate Vector Database Web Viewer...")
        print(f"📱 Server running at: http://localhost:{port}")
        print(f"🔍 Open your browser and navigate to the URL above")
        print(f"⏹️  Press Ctrl+C to stop the server")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print(f"\n👋 Server stopped.")

if __name__ == "__main__":
    start_server()
