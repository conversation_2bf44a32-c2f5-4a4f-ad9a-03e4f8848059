"""
Weaviate Vector Database Viewer
A utility script to view and query the vector database contents.
"""

from openai import OpenAI
import weaviate
import json

# Initialize OpenAI client
client_openai = OpenAI(
    api_key="********************************************************************************************************************************************************************"
)

# Weaviate configuration
WEAVIATE_CLASS_NAME = "ChunkEmbeddings"

def get_embedding(text):
    """Get vector embeddings for a given text using OpenAI's embedding API."""
    response = client_openai.embeddings.create(
        input=text,
        model="text-embedding-ada-002"
    )
    return response.data[0].embedding

def check_weaviate_connection():
    """Check if Weaviate is running and accessible."""
    try:
        with weaviate.connect_to_local() as client:
            if client.is_ready():
                print("✅ Weaviate is running and accessible")
                return True
            else:
                print("❌ Weaviate is not ready")
                return False
    except Exception as e:
        print(f"❌ Cannot connect to Weaviate: {e}")
        print("💡 Make sure Weaviate is running with: docker-compose up -d")
        return False

def view_collection_stats(client):
    """Display statistics about the collection."""
    try:
        collection = client.collections.get(WEAVIATE_CLASS_NAME)
        
        # Get collection info
        response = collection.aggregate.over_all(
            total_count=True
        )
        
        print(f"\n📊 Collection Statistics:")
        print(f"Collection Name: {WEAVIATE_CLASS_NAME}")
        print(f"Total Objects: {response.total_count}")
        
        # Get unique source files
        objects = collection.query.fetch_objects(limit=1000)
        source_files = set()
        chunk_counts = {}
        
        for obj in objects.objects:
            source_file = obj.properties['source_file']
            source_files.add(source_file)
            chunk_counts[source_file] = chunk_counts.get(source_file, 0) + 1
        
        print(f"Unique Source Files: {len(source_files)}")
        print("\n📄 Files and Chunk Counts:")
        for file, count in chunk_counts.items():
            print(f"  • {file}: {count} chunks")
            
    except Exception as e:
        print(f"❌ Error viewing collection stats: {e}")

def search_similar_content(client, query_text, limit=5):
    """Search for similar content using vector similarity."""
    try:
        collection = client.collections.get(WEAVIATE_CLASS_NAME)
        
        print(f"🔍 Generating embedding for query...")
        query_embedding = get_embedding(query_text)
        
        print(f"🔍 Searching for similar content...")
        response = collection.query.near_vector(
            near_vector=query_embedding,
            limit=limit,
            return_metadata=weaviate.classes.query.MetadataQuery(distance=True)
        )
        
        print(f"\n🎯 Search Results for: '{query_text}'")
        print(f"Found {len(response.objects)} similar chunks:")
        print("-" * 80)
        
        for i, obj in enumerate(response.objects, 1):
            distance = obj.metadata.distance if obj.metadata else "N/A"
            similarity = f"{(1 - distance) * 100:.1f}%" if distance != "N/A" else "N/A"
            
            print(f"\n{i}. Similarity: {similarity} (Distance: {distance:.4f})")
            print(f"   📁 Source: {obj.properties['source_file']}")
            print(f"   📄 Chunk: {obj.properties['chunk_number']}")
            print(f"   📝 Content:")
            print(f"      {obj.properties['content'][:300]}...")
            print("-" * 80)
            
    except Exception as e:
        print(f"❌ Error searching content: {e}")

def list_objects_by_file(client, source_file=None, limit=10):
    """List objects, optionally filtered by source file."""
    try:
        collection = client.collections.get(WEAVIATE_CLASS_NAME)
        
        if source_file:
            # Filter by source file
            response = collection.query.fetch_objects(
                where=weaviate.classes.query.Filter.by_property("source_file").equal(source_file),
                limit=limit
            )
            print(f"\n📋 Objects from '{source_file}' (showing first {limit}):")
        else:
            response = collection.query.fetch_objects(limit=limit)
            print(f"\n📋 All Objects (showing first {limit}):")
        
        for i, obj in enumerate(response.objects, 1):
            print(f"\n{i}. UUID: {obj.uuid}")
            print(f"   📁 File: {obj.properties['source_file']}")
            print(f"   📄 Chunk: {obj.properties['chunk_number']}")
            print(f"   📝 Content: {obj.properties['content'][:150]}...")
            
    except Exception as e:
        print(f"❌ Error listing objects: {e}")

def get_available_files(client):
    """Get list of available source files."""
    try:
        collection = client.collections.get(WEAVIATE_CLASS_NAME)
        objects = collection.query.fetch_objects(limit=1000)
        
        source_files = set()
        for obj in objects.objects:
            source_files.add(obj.properties['source_file'])
        
        return sorted(list(source_files))
    except Exception as e:
        print(f"❌ Error getting file list: {e}")
        return []

def interactive_menu():
    """Interactive menu for viewing and querying the vector database."""
    
    # Check connection first
    if not check_weaviate_connection():
        return
    
    with weaviate.connect_to_local() as client:
        # Check if collection exists
        if not client.collections.exists(WEAVIATE_CLASS_NAME):
            print(f"❌ Collection '{WEAVIATE_CLASS_NAME}' does not exist.")
            print("Run vector_embedding.py first to create and populate the database.")
            return
        
        while True:
            print("\n" + "="*60)
            print("🗄️  WEAVIATE VECTOR DATABASE VIEWER")
            print("="*60)
            print("1. View Collection Statistics")
            print("2. List All Objects (first 10)")
            print("3. List Objects by Source File")
            print("4. Search Similar Content")
            print("5. Check Weaviate Status")
            print("6. Exit")
            print("-"*60)
            
            choice = input("Enter your choice (1-6): ").strip()
            
            if choice == "1":
                view_collection_stats(client)
                
            elif choice == "2":
                list_objects_by_file(client)
                
            elif choice == "3":
                files = get_available_files(client)
                if files:
                    print("\nAvailable files:")
                    for i, file in enumerate(files, 1):
                        print(f"{i}. {file}")
                    
                    try:
                        file_choice = int(input(f"\nSelect file (1-{len(files)}): ")) - 1
                        if 0 <= file_choice < len(files):
                            selected_file = files[file_choice]
                            limit = input("Number of objects to show (default 10): ").strip()
                            limit = int(limit) if limit.isdigit() else 10
                            list_objects_by_file(client, selected_file, limit)
                        else:
                            print("❌ Invalid selection.")
                    except ValueError:
                        print("❌ Please enter a valid number.")
                else:
                    print("❌ No files found in the database.")
                    
            elif choice == "4":
                query = input("\nEnter search query: ").strip()
                if query:
                    limit = input("Number of results (default 5): ").strip()
                    limit = int(limit) if limit.isdigit() else 5
                    search_similar_content(client, query, limit)
                else:
                    print("❌ Please enter a valid query.")
                    
            elif choice == "5":
                check_weaviate_connection()
                
            elif choice == "6":
                print("👋 Goodbye!")
                break
                
            else:
                print("❌ Invalid choice. Please enter 1-6.")

if __name__ == "__main__":
    print("🚀 Starting Weaviate Vector Database Viewer...")
    interactive_menu()
