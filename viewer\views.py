from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from openai import OpenAI
import weaviate
import json

# Initialize OpenAI client
client_openai = OpenAI(
    api_key="********************************************************************************************************************************************************************"
)

# Weaviate configuration
WEAVIATE_CLASS_NAME = "ChunkEmbeddings"

def get_embedding(text):
    """Get vector embeddings for a given text using OpenAI's embedding API."""
    response = client_openai.embeddings.create(
        input=text,
        model="text-embedding-ada-002"
    )
    return response.data[0].embedding

def get_weaviate_client():
    """Get Weaviate client connection."""
    return weaviate.connect_to_local()

def index(request):
    """Main dashboard page."""
    return render(request, 'viewer/index.html')

def get_stats(request):
    """Get collection statistics."""
    try:
        with get_weaviate_client() as client:
            if not client.collections.exists(WEAVIATE_CLASS_NAME):
                return JsonResponse({'error': 'Collection does not exist'})

            collection = client.collections.get(WEAVIATE_CLASS_NAME)

            # Get total count
            response = collection.aggregate.over_all(total_count=True)
            total_count = response.total_count

            # Get file statistics
            objects = collection.query.fetch_objects(limit=1000)
            source_files = {}

            for obj in objects.objects:
                source_file = obj.properties['source_file']
                source_files[source_file] = source_files.get(source_file, 0) + 1

            return JsonResponse({
                'total_objects': total_count,
                'total_files': len(source_files),
                'files': source_files
            })
    except Exception as e:
        return JsonResponse({'error': str(e)})

def get_objects(request):
    """Get objects with pagination."""
    try:
        limit = int(request.GET.get('limit', 10))
        source_file = request.GET.get('source_file', None)

        with get_weaviate_client() as client:
            collection = client.collections.get(WEAVIATE_CLASS_NAME)

            if source_file:
                response = collection.query.fetch_objects(
                    where=weaviate.classes.query.Filter.by_property("source_file").equal(source_file),
                    limit=limit
                )
            else:
                response = collection.query.fetch_objects(limit=limit)

            objects = []
            for obj in response.objects:
                objects.append({
                    'uuid': str(obj.uuid),
                    'source_file': obj.properties['source_file'],
                    'chunk_number': obj.properties['chunk_number'],
                    'content': obj.properties['content'][:200] + '...' if len(obj.properties['content']) > 200 else obj.properties['content']
                })

            return JsonResponse({'objects': objects})
    except Exception as e:
        return JsonResponse({'error': str(e)})

@csrf_exempt
@require_http_methods(["POST"])
def search(request):
    """Search for similar content."""
    try:
        data = json.loads(request.body)
        query = data.get('query', '')
        limit = int(data.get('limit', 5))

        if not query:
            return JsonResponse({'error': 'Query is required'})

        with get_weaviate_client() as client:
            collection = client.collections.get(WEAVIATE_CLASS_NAME)

            # Get embedding for query
            query_embedding = get_embedding(query)

            # Search
            response = collection.query.near_vector(
                near_vector=query_embedding,
                limit=limit,
                return_metadata=weaviate.classes.query.MetadataQuery(distance=True)
            )

            results = []
            for obj in response.objects:
                distance = obj.metadata.distance if obj.metadata else None
                similarity = (1 - distance) * 100 if distance is not None else None

                results.append({
                    'uuid': str(obj.uuid),
                    'source_file': obj.properties['source_file'],
                    'chunk_number': obj.properties['chunk_number'],
                    'content': obj.properties['content'],
                    'distance': distance,
                    'similarity': similarity
                })

            return JsonResponse({'results': results})
    except Exception as e:
        return JsonResponse({'error': str(e)})

def health_check(request):
    """Check if Weaviate is accessible."""
    try:
        with get_weaviate_client() as client:
            if client.is_ready():
                return JsonResponse({'status': 'healthy', 'message': 'Weaviate is accessible'})
            else:
                return JsonResponse({'status': 'unhealthy', 'message': 'Weaviate is not ready'})
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)})
