<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weaviate Vector Console</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Courier New', monospace;
            background: #1a1a1a; color: #00ff00; padding: 20px;
        }
        .container { max-width: 1400px; margin: 0 auto; }
        .header {
            background: #000; padding: 20px; border: 2px solid #00ff00;
            margin-bottom: 20px; text-align: center;
        }
        .header h1 { color: #00ff00; font-size: 2em; }
        .controls {
            background: #2a2a2a; padding: 15px; border: 1px solid #555;
            margin-bottom: 20px; display: flex; gap: 10px; align-items: center;
        }
        .btn {
            background: #00ff00; color: #000; border: none; padding: 8px 16px;
            cursor: pointer; font-family: inherit; font-weight: bold;
        }
        .btn:hover { background: #00cc00; }
        .input { background: #000; color: #00ff00; border: 1px solid #00ff00; padding: 8px; }
        .vector-container {
            background: #000; border: 2px solid #00ff00; padding: 20px;
            margin-bottom: 20px; max-height: 600px; overflow-y: auto;
        }
        .vector-item {
            border-bottom: 1px solid #333; padding: 15px 0;
            margin-bottom: 15px;
        }
        .vector-item:last-child { border-bottom: none; }
        .vector-meta {
            color: #ffff00; margin-bottom: 10px; font-weight: bold;
        }
        .vector-data {
            background: #111; padding: 10px; border-left: 3px solid #00ff00;
            word-break: break-all; font-size: 0.9em; line-height: 1.4;
        }
        .vector-preview {
            background: #222; padding: 8px; margin: 5px 0;
            border-left: 2px solid #ffff00;
        }
        .loading { text-align: center; color: #ffff00; padding: 20px; }
        .error { color: #ff0000; background: #330000; padding: 10px; border: 1px solid #ff0000; }
        .stats {
            display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px; margin-bottom: 20px;
        }
        .stat-card {
            background: #2a2a2a; border: 1px solid #00ff00; padding: 15px; text-align: center;
        }
        .stat-value { font-size: 1.5em; color: #00ff00; font-weight: bold; }
        .stat-label { color: #ccc; margin-top: 5px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧮 WEAVIATE VECTOR CONSOLE</h1>
            <p>Direct REST API Access to Vector Embeddings</p>
        </div>

        <div class="stats" id="stats">
            <div class="stat-card">
                <div class="stat-value" id="total-count">Loading...</div>
                <div class="stat-label">Total Vectors</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="vector-dims">Loading...</div>
                <div class="stat-label">Vector Dimensions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="connection-status">Checking...</div>
                <div class="stat-label">Connection Status</div>
            </div>
        </div>

        <div class="controls">
            <button class="btn" onclick="loadVectors()">Load Vectors</button>
            <button class="btn" onclick="loadSchema()">Show Schema</button>
            <input type="number" class="input" id="limit" value="5" min="1" max="50" placeholder="Limit">
            <button class="btn" onclick="clearDisplay()">Clear</button>
        </div>

        <div class="vector-container" id="output">
            <div class="loading">Click "Load Vectors" to view vector embeddings</div>
        </div>
    </div>

    <script>
        const WEAVIATE_URL = 'http://localhost:8080';
        const CLASS_NAME = 'ChunkEmbeddings';

        async function checkConnection() {
            try {
                const response = await fetch(`${WEAVIATE_URL}/v1/meta`);
                const data = await response.json();
                document.getElementById('connection-status').textContent = '✅ Online';
                document.getElementById('connection-status').style.color = '#00ff00';
                return true;
            } catch (error) {
                document.getElementById('connection-status').textContent = '❌ Offline';
                document.getElementById('connection-status').style.color = '#ff0000';
                return false;
            }
        }

        async function loadSchema() {
            const output = document.getElementById('output');
            output.innerHTML = '<div class="loading">Loading schema...</div>';

            try {
                const response = await fetch(`${WEAVIATE_URL}/v1/schema`);
                const data = await response.json();
                
                let html = '<div class="vector-item">';
                html += '<div class="vector-meta">📋 WEAVIATE SCHEMA</div>';
                html += '<div class="vector-data">' + JSON.stringify(data, null, 2) + '</div>';
                html += '</div>';
                
                output.innerHTML = html;
            } catch (error) {
                output.innerHTML = `<div class="error">Error loading schema: ${error.message}</div>`;
            }
        }

        async function loadVectors() {
            const limit = document.getElementById('limit').value || 5;
            const output = document.getElementById('output');
            output.innerHTML = '<div class="loading">Loading vectors...</div>';

            try {
                const response = await fetch(`${WEAVIATE_URL}/v1/objects?class=${CLASS_NAME}&limit=${limit}&include=vector`);
                const data = await response.json();
                
                if (!data.objects || data.objects.length === 0) {
                    output.innerHTML = '<div class="error">No vectors found</div>';
                    return;
                }

                let html = '';
                data.objects.forEach((obj, index) => {
                    const vector = obj.vector || [];
                    const props = obj.properties || {};
                    
                    html += '<div class="vector-item">';
                    html += `<div class="vector-meta">🧮 VECTOR ${index + 1} | UUID: ${obj.id}</div>`;
                    
                    // Metadata
                    html += '<div class="vector-preview">';
                    html += `📁 File: ${props.source_file || 'N/A'}<br>`;
                    html += `📄 Chunk: ${props.chunk_number || 'N/A'}<br>`;
                    html += `📊 Dimensions: ${vector.length}<br>`;
                    html += `📝 Content: ${(props.content || '').substring(0, 100)}...`;
                    html += '</div>';
                    
                    // Vector preview (first 20 dimensions)
                    if (vector.length > 0) {
                        const preview = vector.slice(0, 20).map(v => v.toFixed(6)).join(', ');
                        html += '<div class="vector-preview">';
                        html += `🔢 Vector Preview (first 20): [${preview}${vector.length > 20 ? ', ...' : ''}]`;
                        html += '</div>';
                        
                        // Full vector (collapsible)
                        html += `<div class="vector-data" id="vector-${index}" style="display: none;">`;
                        html += `<strong>Full Vector (${vector.length} dimensions):</strong><br>`;
                        html += '[' + vector.map(v => v.toFixed(6)).join(', ') + ']';
                        html += '</div>';
                        
                        html += `<button class="btn" onclick="toggleVector(${index})" style="margin-top: 10px;">Show/Hide Full Vector</button>`;
                    } else {
                        html += '<div class="error">No vector data found</div>';
                    }
                    
                    html += '</div>';
                });
                
                output.innerHTML = html;
                
                // Update stats
                document.getElementById('total-count').textContent = data.objects.length;
                if (data.objects.length > 0 && data.objects[0].vector) {
                    document.getElementById('vector-dims').textContent = data.objects[0].vector.length;
                }
                
            } catch (error) {
                output.innerHTML = `<div class="error">Error loading vectors: ${error.message}</div>`;
            }
        }

        function toggleVector(index) {
            const vectorDiv = document.getElementById(`vector-${index}`);
            if (vectorDiv.style.display === 'none') {
                vectorDiv.style.display = 'block';
            } else {
                vectorDiv.style.display = 'none';
            }
        }

        function clearDisplay() {
            document.getElementById('output').innerHTML = '<div class="loading">Display cleared. Click "Load Vectors" to view data.</div>';
        }

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            checkConnection();
        });
    </script>
</body>
</html>
