"""
Web-based Weaviate Vector Database Viewer
A Flask web application to view and query the vector database.
"""

from flask import Flask, render_template, request, jsonify
from openai import OpenAI
import weaviate
import json

app = Flask(__name__)

# Initialize OpenAI client
client_openai = OpenAI(
    api_key="********************************************************************************************************************************************************************"
)

# Weaviate configuration
WEAVIATE_CLASS_NAME = "ChunkEmbeddings"

def get_embedding(text):
    """Get vector embeddings for a given text using OpenAI's embedding API."""
    response = client_openai.embeddings.create(
        input=text,
        model="text-embedding-ada-002"
    )
    return response.data[0].embedding

def get_weaviate_client():
    """Get Weaviate client connection."""
    return weaviate.connect_to_local()

@app.route('/')
def index():
    """Main dashboard page."""
    return render_template('index.html')

@app.route('/api/stats')
def get_stats():
    """Get collection statistics."""
    try:
        with get_weaviate_client() as client:
            if not client.collections.exists(WEAVIATE_CLASS_NAME):
                return jsonify({'error': 'Collection does not exist'})
            
            collection = client.collections.get(WEAVIATE_CLASS_NAME)
            
            # Get total count
            response = collection.aggregate.over_all(total_count=True)
            total_count = response.total_count
            
            # Get file statistics
            objects = collection.query.fetch_objects(limit=1000)
            source_files = {}
            
            for obj in objects.objects:
                source_file = obj.properties['source_file']
                source_files[source_file] = source_files.get(source_file, 0) + 1
            
            return jsonify({
                'total_objects': total_count,
                'total_files': len(source_files),
                'files': source_files
            })
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/api/objects')
def get_objects():
    """Get objects with pagination."""
    try:
        limit = int(request.args.get('limit', 10))
        source_file = request.args.get('source_file', None)
        
        with get_weaviate_client() as client:
            collection = client.collections.get(WEAVIATE_CLASS_NAME)
            
            if source_file:
                response = collection.query.fetch_objects(
                    where=weaviate.classes.query.Filter.by_property("source_file").equal(source_file),
                    limit=limit
                )
            else:
                response = collection.query.fetch_objects(limit=limit)
            
            objects = []
            for obj in response.objects:
                objects.append({
                    'uuid': str(obj.uuid),
                    'source_file': obj.properties['source_file'],
                    'chunk_number': obj.properties['chunk_number'],
                    'content': obj.properties['content'][:200] + '...' if len(obj.properties['content']) > 200 else obj.properties['content']
                })
            
            return jsonify({'objects': objects})
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/api/search', methods=['POST'])
def search():
    """Search for similar content."""
    try:
        data = request.get_json()
        query = data.get('query', '')
        limit = int(data.get('limit', 5))
        
        if not query:
            return jsonify({'error': 'Query is required'})
        
        with get_weaviate_client() as client:
            collection = client.collections.get(WEAVIATE_CLASS_NAME)
            
            # Get embedding for query
            query_embedding = get_embedding(query)
            
            # Search
            response = collection.query.near_vector(
                near_vector=query_embedding,
                limit=limit,
                return_metadata=weaviate.classes.query.MetadataQuery(distance=True)
            )
            
            results = []
            for obj in response.objects:
                distance = obj.metadata.distance if obj.metadata else None
                similarity = (1 - distance) * 100 if distance is not None else None
                
                results.append({
                    'uuid': str(obj.uuid),
                    'source_file': obj.properties['source_file'],
                    'chunk_number': obj.properties['chunk_number'],
                    'content': obj.properties['content'],
                    'distance': distance,
                    'similarity': similarity
                })
            
            return jsonify({'results': results})
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/api/health')
def health_check():
    """Check if Weaviate is accessible."""
    try:
        with get_weaviate_client() as client:
            if client.is_ready():
                return jsonify({'status': 'healthy', 'message': 'Weaviate is accessible'})
            else:
                return jsonify({'status': 'unhealthy', 'message': 'Weaviate is not ready'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

if __name__ == '__main__':
    print("🚀 Starting Weaviate Web Viewer...")
    print("📱 Open your browser and go to: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
